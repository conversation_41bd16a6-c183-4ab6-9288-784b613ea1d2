; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="ArchipelagoGame"
run/main_scene="uid://3ts1illrf1vt"
config/features=PackedStringArray("4.4", "C#")

[autoload]

InteractionManager="*res://src/Script/manager/InteractionManager.cs"

[display]

window/stretch/mode="canvas_items"

[dotnet]

project/assembly_name="UnnamedProject"

[editor_plugins]

enabled=PackedStringArray()

[layer_names]

2d_render/layer_1="player"
2d_render/layer_2="enemy"
2d_render/layer_3="Interactable"
2d_render/layer_4="building"
2d_physics/layer_1="player"
2d_physics/layer_2="enemy"
2d_physics/layer_3="Interactable"
2d_physics/layer_4="building"

[rendering]

driver/stretch/mode="disabled"
