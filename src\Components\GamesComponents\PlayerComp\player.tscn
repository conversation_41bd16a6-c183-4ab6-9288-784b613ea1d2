[gd_scene load_steps=14 format=3 uid="uid://oys430ji88lr"]

[ext_resource type="Script" uid="uid://bbtuesaulb563" path="res://src/Script/Player/PlayerController.cs" id="1_lh30n"]
[ext_resource type="Texture2D" uid="uid://d1wp8c43lbv40" path="res://Assets/image/player_image/Dash.png" id="5_lh30n"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_k2co4"]
radius = 18.0
height = 48.0

[sub_resource type="Animation" id="Animation_1ug34"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [16]
}

[sub_resource type="Animation" id="Animation_3thf5"]
resource_name = "down_idle"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_bjd4v"]
resource_name = "down_move"
length = 0.8
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [1, 2, 3, 4, 5, 6, 7, 8]
}

[sub_resource type="Animation" id="Animation_rtph7"]
resource_name = "left_idle"
length = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [8]
}

[sub_resource type="Animation" id="Animation_enw2e"]
resource_name = "leftmove"
length = 0.8
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [9, 10, 11, 12, 13, 14, 15, 9]
}

[sub_resource type="Animation" id="Animation_6jv0w"]
resource_name = "right_idle"
length = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [40]
}

[sub_resource type="Animation" id="Animation_q2myn"]
resource_name = "right_move"
length = 0.8
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [41, 42, 43, 44, 45, 46, 47, 47]
}

[sub_resource type="Animation" id="Animation_21odt"]
resource_name = "up_idle"
length = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [24]
}

[sub_resource type="Animation" id="Animation_lh30n"]
resource_name = "up_move"
length = 0.8
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [25, 26, 27, 28, 29, 30, 31, 32]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_cqylb"]
_data = {
&"RESET": SubResource("Animation_1ug34"),
&"down_idle": SubResource("Animation_3thf5"),
&"down_move": SubResource("Animation_bjd4v"),
&"left_idle": SubResource("Animation_rtph7"),
&"left_move": SubResource("Animation_enw2e"),
&"right_idle": SubResource("Animation_6jv0w"),
&"right_move": SubResource("Animation_q2myn"),
&"up_idle": SubResource("Animation_21odt"),
&"up_move": SubResource("Animation_lh30n")
}

[node name="PlayerBehavior" type="Node2D"]

[node name="Player" type="CharacterBody2D" parent="."]
position = Vector2(414, 408)
collision_mask = 14
script = ExtResource("1_lh30n")

[node name="PlayerSprite" type="Sprite2D" parent="Player"]
texture_filter = 1
scale = Vector2(5, 5)
texture = ExtResource("5_lh30n")
hframes = 8
vframes = 6
frame = 16

[node name="PlayerCollisionShap2D" type="CollisionShape2D" parent="Player"]
z_index = 2
position = Vector2(3, 31)
shape = SubResource("CapsuleShape2D_k2co4")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Player"]
libraries = {
&"": SubResource("AnimationLibrary_cqylb")
}
