/*
 * 玩家控制器 - 等距视角移动系统
 */

using Godot;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家控制器 - 实现等距视角的角色移动
    /// 继承自CharacterBody2D以获得物理移动和碰撞检测功能
    /// </summary>
    public partial class PlayerController : CharacterBody2D
    {

        [Export] public float Speed = 300.0f; // 移动速度（像素/秒）
        public CharacterBody2D Player;
        public AnimationPlayer PlayerAnimation;

        private string _lastMoveDirection = "down"; // 默认面朝下方
        public override void _Ready()
        {
            Player = GetNode<CharacterBody2D>("Player");
            PlayerAnimation = GetNode<AnimationPlayer>("AnimationPlayer");
        }

        public override void _PhysicsProcess(double delta)
        {
            // 1. 获取输入方向（-1到1之间的值）
            Vector2 inputDirection = Input.GetVector("ui_left", "ui_right", "ui_up", "ui_down");
            Velocity = inputDirection * Speed;
            MoveAndSlide();

            // 2. 动画控制
            if (inputDirection != Vector2.Zero)
            {
                // 移动时更新方向并播放移动动画
                _lastMoveDirection = GetDirectionFromVector(inputDirection, isIdle: false);
                PlayerAnimation.Play(_lastMoveDirection);
            }
            else
            {
                // 静止时播放对应方向的待机动画
                string idleAnimation = $"{_lastMoveDirection.Replace("_move", "")}_idle";
                PlayerAnimation.Play(idleAnimation);
            }
        }

        // 通过向量分量判断8方向
        private string GetDirectionFromVector(Vector2 dir, bool isIdle)
        {
            // // 斜方向优先判断（对角线）
            // if (dir.X > 0.5f && dir.Y < -0.5f) return "up_right_move";    // 右上移动
            // if (dir.X > 0.5f && dir.Y > 0.5f) return "down_right_move"; // 右下移动
            // if (dir.X < -0.5f && dir.Y > 0.5f) return "down_left_move";  // 左下移动
            // if (dir.X < -0.5f && dir.Y < -0.5f) return "up_left_move";    // 左上移动

            // 基本方向判断
            if (Mathf.Abs(dir.X) > Mathf.Abs(dir.Y))
                return dir.X > 0 ? "right_move" : "left_move"; // 水平优先
            else
                return dir.Y > 0 ? "down_move" : "up_move";    // 垂直优先
        }
    }
}
